import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../navigation/app_router.dart';
import '../design_system/components/reusable_components.dart';

/// Top-level function to handle background messages
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  debugPrint('Handling background message: ${message.messageId}');
  // Handle background message
  await FCMService._handleBackgroundMessage(message);
}

/// Service to handle Firebase Cloud Messaging (FCM) for push notifications
class FCMService {
  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications = 
      FlutterLocalNotificationsPlugin();
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  
  static String? _currentToken;
  static BuildContext? _appContext;

  /// Initialize FCM service
  static Future<void> initialize(BuildContext appContext) async {
    _appContext = appContext;
    
    // Initialize local notifications
    await _initializeLocalNotifications();
    
    // Set up FCM handlers
    await _setupFCMHandlers();
    
    // Get and store FCM token
    await _handleTokenRefresh();
    
    debugPrint('FCM Service initialized successfully');
  }

  /// Initialize local notifications plugin
  static Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: false,
      requestBadgePermission: false,
      requestSoundPermission: false,
    );
    
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );
    
    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }

  /// Set up FCM message handlers
  static Future<void> _setupFCMHandlers() async {
    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
    
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);
    
    // Handle token refresh
    FirebaseMessaging.instance.onTokenRefresh.listen(_onTokenRefresh);
  }

  /// Get current FCM token
  static Future<String?> getToken() async {
    try {
      _currentToken = await _firebaseMessaging.getToken();
      return _currentToken;
    } catch (e) {
      debugPrint('Error getting FCM token: $e');
      return null;
    }
  }

  /// Handle FCM token refresh and storage
  static Future<void> _handleTokenRefresh() async {
    final token = await getToken();
    if (token != null) {
      await _storeTokenInFirestore(token);
    }
  }

  /// Store FCM token in Firestore for the current user
  static Future<void> _storeTokenInFirestore(String token) async {
    try {
      final user = _auth.currentUser;
      if (user != null) {
        await _firestore.collection('users').doc(user.uid).update({
          'fcmToken': token,
          'tokenUpdatedAt': FieldValue.serverTimestamp(),
        });
        debugPrint('FCM token stored in Firestore');
      }
    } catch (e) {
      debugPrint('Error storing FCM token: $e');
    }
  }

  /// Handle token refresh event
  static Future<void> _onTokenRefresh(String token) async {
    _currentToken = token;
    await _storeTokenInFirestore(token);
  }

  /// Handle foreground messages (when app is open)
  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    debugPrint('Received foreground message: ${message.messageId}');
    
    // Show local notification for foreground messages
    await _showLocalNotification(message);
    
    // Update in-app notification badge or UI if needed
    await _createInAppNotification(message);
  }

  /// Handle background messages
  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    debugPrint('Handling background message: ${message.messageId}');
    
    // Create in-app notification for when user opens app
    await _createInAppNotification(message);
  }

  /// Handle notification tap when app was in background
  static Future<void> _handleMessageOpenedApp(RemoteMessage message) async {
    debugPrint('Message opened app: ${message.messageId}');
    
    // Navigate based on notification data
    await _navigateFromNotification(message);
  }

  /// Show local notification
  static Future<void> _showLocalNotification(RemoteMessage message) async {
    final notification = message.notification;
    final data = message.data;
    
    if (notification != null) {
      final type = data['type'] ?? 'system';
      final channelId = _getChannelId(type);
      final channelName = _getChannelName(type);
      
      final androidDetails = AndroidNotificationDetails(
        channelId,
        channelName,
        channelDescription: 'Notifications for $channelName',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
        color: const Color(0xFFB45309), // AppTheme.accentCopper
        showWhen: true,
      );
      
      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );
      
      final details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );
      
      await _localNotifications.show(
        message.hashCode,
        notification.title,
        notification.body,
        details,
        payload: jsonEncode(data),
      );
    }
  }

  /// Create in-app notification in Firestore
  static Future<void> _createInAppNotification(RemoteMessage message) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;
      
      final notification = message.notification;
      final data = message.data;
      
      if (notification != null) {
        await _firestore.collection('notifications').add({
          'userId': user.uid,
          'title': notification.title ?? 'Notification',
          'message': notification.body ?? '',
          'type': data['type'] ?? 'system',
          'isRead': false,
          'timestamp': FieldValue.serverTimestamp(),
          'data': data,
          'messageId': message.messageId,
        });
      }
    } catch (e) {
      debugPrint('Error creating in-app notification: $e');
    }
  }

  /// Handle notification tap from local notification
  static void _onNotificationTapped(NotificationResponse response) {
    if (response.payload != null && _appContext != null) {
      try {
        final data = jsonDecode(response.payload!);
        final message = RemoteMessage(data: Map<String, String>.from(data));
        _navigateFromNotification(message);
      } catch (e) {
        debugPrint('Error handling notification tap: $e');
      }
    }
  }

  /// Navigate based on notification data
  static Future<void> _navigateFromNotification(RemoteMessage message) async {
    if (_appContext == null) return;
    
    final data = message.data;
    final type = data['type'] ?? 'system';
    final actionUrl = data['actionUrl'];
    
    try {
      if (actionUrl != null && actionUrl.isNotEmpty) {
        // Use custom action URL if provided
        _appContext!.go(actionUrl);
      } else {
        // Default navigation based on type
        switch (type) {
          case 'jobs':
            _appContext!.go(AppRouter.jobs);
            break;
          case 'safety':
            _appContext!.go(AppRouter.notifications);
            break;
          case 'applications':
            _appContext!.go(AppRouter.notifications);
            break;
          case 'storm':
            _appContext!.go(AppRouter.storm);
            break;
          default:
            _appContext!.go(AppRouter.notifications);
        }
      }
    } catch (e) {
      debugPrint('Error navigating from notification: $e');
      // Fallback to notifications screen
      _appContext!.go(AppRouter.notifications);
    }
  }

  /// Get notification channel ID based on type
  static String _getChannelId(String type) {
    switch (type) {
      case 'jobs':
        return 'job_alerts';
      case 'safety':
        return 'safety_alerts';
      case 'storm':
        return 'storm_alerts';
      case 'applications':
        return 'application_updates';
      default:
        return 'general_notifications';
    }
  }

  /// Get notification channel name based on type
  static String _getChannelName(String type) {
    switch (type) {
      case 'jobs':
        return 'Job Alerts';
      case 'safety':
        return 'Safety Alerts';
      case 'storm':
        return 'Storm Work';
      case 'applications':
        return 'Application Updates';
      default:
        return 'General';
    }
  }

  /// Send targeted notification (for testing or admin use)
  static Future<void> sendNotificationToUser({
    required String userId,
    required String title,
    required String body,
    required String type,
    Map<String, String>? additionalData,
  }) async {
    try {
      // Get user's FCM token
      final userDoc = await _firestore.collection('users').doc(userId).get();
      final fcmToken = userDoc.data()?['fcmToken'] as String?;
      
      if (fcmToken == null) {
        debugPrint('No FCM token found for user: $userId');
        return;
      }
      
      // Create notification data
      final data = {
        'type': type,
        'userId': userId,
        'title': title,
        'body': body,
        ...?additionalData,
      };
      
      // Also create in-app notification
      await _firestore.collection('notifications').add({
        'userId': userId,
        'title': title,
        'message': body,
        'type': type,
        'isRead': false,
        'timestamp': FieldValue.serverTimestamp(),
        'data': data,
      });
      
      debugPrint('Notification queued for user: $userId');
    } catch (e) {
      debugPrint('Error sending notification: $e');
    }
  }

  /// Subscribe to topic for broadcast notifications
  static Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      debugPrint('Subscribed to topic: $topic');
    } catch (e) {
      debugPrint('Error subscribing to topic $topic: $e');
    }
  }

  /// Unsubscribe from topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      debugPrint('Unsubscribed from topic: $topic');
    } catch (e) {
      debugPrint('Error unsubscribing from topic $topic: $e');
    }
  }

  /// Clear app badge (iOS)
  static Future<void> clearBadge() async {
    try {
      await _firebaseMessaging.setApplicationBadgeCount(0);
    } catch (e) {
      debugPrint('Error clearing badge: $e');
    }
  }
}